import os
import time
from typing import Any

from langchain.chat_models import init_chat_model
from langgraph.prebuilt import create_react_agent
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_core.callbacks import Callbacks
from langchain.schema.runnable import RunnableConfig

from contextlib import asynccontextmanager

from galileo import galileo_context
from galileo.handlers.langchain import GalileoAsyncCallback

from agent.util.prompt import PROMPT
from agent.util.tools import TOOLS
from agent.util.states import State

#Initialize the Galileo callback handler
external_id = f"sales-agent-{int(time.time())}"
galileo_context.start_session(name="test", external_id=external_id)
galileo_callback = GalileoAsyncCallback()

def get_default_config(thread_id: Any = 0) -> RunnableConfig:
    """Get the default runnable config with Galileo callback."""
    config: dict[str, Any] = {"configurable": {"thread_id": thread_id}}
    callbacks: Callbacks = [galileo_callback]
    return RunnableConfig(callbacks=callbacks, **config)

@asynccontextmanager
async def make_graph():
    mcp_client = MultiServerMCPClient(
        {
            "pricing": {
                "url": "http://mcp-pricing:8000/mcp/",
                "transport": "streamable_http",
            },
            "search": {
                "url": "http://mcp-search:8000/mcp/",
                "transport": "streamable_http",
            }
        }
    )

    # Gather all tools for the agent
    tools = []
    tools.extend(TOOLS)

    pricing_tools = await mcp_client.get_tools(server_name="pricing")
    tools.extend(pricing_tools)

    search_tools = await mcp_client.get_tools(server_name="search")
    tools.extend(search_tools)

    #model_name = os.environ.get("CHAT_MODEL")
    chat_model = init_chat_model(model_name, callbacks=[galileo_callback])

    agent = create_react_agent(
        model = chat_model,
        tools = tools,
        prompt = PROMPT,
        state_schema = State,
    )

    config: dict[str, Any] = {"configurable": {"thread_id": 0}}
    callbacks: Callbacks = [galileo_callback]
    runnable_config = RunnableConfig(callbacks=callbacks, **config)

    # Store the default config on the agent for easy access
    agent.default_config = runnable_config

    yield agent